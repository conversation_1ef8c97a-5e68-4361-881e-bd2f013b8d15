﻿using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using FairyGUI;
using UnityEngine;
#if YOOASSET
using YooAsset;
#endif

internal class GameRoot : MonoBehaviour
{
#if YOOASSET
    public EPlayMode playMode = EPlayMode.EditorSimulateMode;
#endif

    private void Awake()
    {
        QualitySettings.vSyncCount = 0;
        Application.targetFrameRate = 30;
        GameObject.DontDestroyOnLoad(this);
        Screen.sleepTimeout = SleepTimeout.NeverSleep;
        // GGISdk.ActiveReport();
    }

    private void Start()
    {
        LoadingPanel.Show();
        LoadingPanel.SetProgress(1);
        Platform.Instance.Init(InitGame);
    }

    private bool _isInit;
    private void InitGame()
    {
        if (_isInit)
            return;
        _isInit = true;

        InitLocalConfig();
        InitAbTest(Platform.Instance.GetAbTest());
        StartCoroutine(OnInitGame());
    }

    private IEnumerator OnInitGame()
    {
        // Platform.Instance.ReportScene(ReportType.InitGame);
#if YOOASSET
        var driver = new YooAssetsInitialize();
#if UNITY_WEBGL && !UNITY_EDITOR
        playMode = EPlayMode.WebPlayMode;
        YooAssets.SetCacheSystemDisableCacheOnWebGL();
#endif
        yield return StartCoroutine(driver.Initialize(playMode, Main.CdnDir, Main.AppendTimeTicks));
        AssetBundleManager.Initialize(Main.BundleDir, driver.package);
#else
        AssetBundleManager.Initialize("Bundles/");
#endif
        // HandlerManager.Inst.Register<LobbyHandler>();
        // HandlerManager.Inst.Register<IdiomMergeHandler>();
        // HandlerManager.Inst.Register<EmptyHandler>();

        UIConfig.modalLayerColor = new Color(0, 0, 0, 0.7f);
        UIConfig.bringWindowToFrontOnClick = false;
        DOTween.SetTweensCapacity(400, 50);
        InitFont();
        LoadConfig();
        GameGlobal.UnNewPlayer();
        yield return null;
    }

    private void InitAbTest(int abTest)
    {
#if UNITY_EDITOR
        GameGlobal.Channel = "A";
        return;
#endif

        Debug.Log("abTest :" + abTest);
        var channel = GameGlobal.Channel = StorageMgr.Channel;
        if (GameGlobal.IsNewPlayer && string.IsNullOrEmpty(channel))
        {
            GameGlobal.Channel = abTest switch
            {
                0 => "A",
                1 => "B",
                2 => "C",
                _ => "A",
            };
        }

        if (string.IsNullOrEmpty(GameGlobal.Channel))
            GameGlobal.Channel = "A";

        Debug.Log("ab_test:" + GameGlobal.Channel);
    }

    private void InitLocalConfig()
    {
        GameGlobal.Read();
        DOTween.SetTweensCapacity(400, 150);
        SoundManager.SetBgmMute(!StorageMgr.SoundBgmOn);
        SoundManager.SetEffectMute(!StorageMgr.SoundEffectOn);
    }

    private void LoadConfig()
    {
        ConfigLoader.Inst.LoadConfigs(InitCommonUI);
    }

    private void InitCommonUI()
    {
        FUILoader.LoadPackages(new List<string> { "Common" }, () =>
        {
            StartGame();
        });
        GRoot.inst.onTouchBegin.Add(OnGolbalTouchBegin);
    }

    private void OnGolbalTouchBegin(EventContext context)
    {
        if (GRoot.inst.touchTarget is GButton)
        {
            SoundManager.PlayEffect("button");
        }
    }

    private void InitFont()
    {
        AssetBundleManager.LoadTMPFont("Fonts/RHRC SDF", (fontRes) =>
        {
            TMPFont font = new TMPFont();
            Log.Info("fontRes:" + fontRes + "  " + font);
            font.name = "RHRC";
            font.fontAsset = fontRes;
            FontManager.RegisterFont(font);

            UIConfig.defaultFont = "RHRC";
        });
    }

    private void StartGame()
    {
        Report.Instance.SetOnceAbTest(GameGlobal.Channel);
        if (GameGlobal.NeedGuide)
        {
            new CmdEnterBattle().Execute(0, true);
        }
        else
        {
            var enterSuccess = false;
            var gameProgress = SystemManager.Inst.GetGlobalSystem<GameProgressSystem>();
            if (gameProgress.RestoreProgress())
            {
                var battleType = gameProgress.GetProgressData().battleType;
                if (battleType == BattleType.Normal)
                {
                    enterSuccess = EnterBattle(true);
                }
                else if (battleType == BattleType.DailyChallenge)
                {
                    enterSuccess = EnterDailyBattle();
                }
            }
            else
            {
                enterSuccess = EnterBattle(false);
            }

            if (!enterSuccess)
            {
                HandlerManager.Inst.SwitchHandler<LobbyHandler>();
            }
        }
    }

    private bool EnterBattle(bool ignoreStaminaCost)
    {
        GameGlobal.BattleType = BattleType.Normal;
        var isEnterSuccess = new CmdEnterBattle().Execute(ignoreStaminaCost: ignoreStaminaCost);
        return isEnterSuccess;
    }

    private bool EnterDailyBattle()
    {
        GameGlobal.BattleType = BattleType.DailyChallenge;
        var isEnterSuccess = new CmdEnterBattle().Execute(SystemFacade.DailyChallengeSystem.GetCurrentChallengeGate(), true);
        return isEnterSuccess;
    }
}
