using UnityEngine;
using DG.Tweening;
using Cysharp.Threading.Tasks;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

public class TileAnimationManager
{
    private readonly Board board;
    private readonly int innerGridWidth;
    private readonly int innerGridHeight;

    public TileAnimationManager(Board board, int innerGridWidth, int innerGridHeight)
    {
        this.board = board;
        this.innerGridWidth = innerGridWidth;
        this.innerGridHeight = innerGridHeight;
    }

    /// <summary>
    /// 随机所有动画
    /// </summary>
    /// <returns></returns>
    internal async UniTask RandomIn()
    {
        var random = UnityEngine.Random.Range(0, 4);
        switch (random)
        {
            case 0:
                await RollInFromRight();
                break;
            case 1:
                await DiagonalIn();
                break;
            case 2:
                await ExpandFromCenter();
                break;
            case 3:
                await WaveDrop();
                break;
        }
    }

    /// <summary>
    /// 从右侧滚动进入
    /// </summary>
    public async UniTask RollInFromRight()
    {
        const float duration = 0.8f;
        const float delay = 0.04f;

        for (int y = 1; y <= innerGridHeight; y++)
        {
            for (int x = 1; x <= innerGridWidth; x++)
            {
                var tile = board.grid[x, y].tile;
                if (tile == null)
                    continue;

                var targetPos = tile.transform.localPosition;
                var startPos = targetPos;
                // 根据行数的奇偶性决定从左边还是右边进入
                float tileDelay;
                Vector3 rotationAxis;
                // if (y % 2 == 1)
                // {
                // 奇数行从右边进入
                startPos.x += 10f;
                tileDelay = delay * (x - 1);
                rotationAxis = Vector3.forward;
                // }
                // else
                // {
                //     // 偶数行从左边进入
                //     startPos.x -= 10f;
                //     tileDelay = delay * (innerGridWidth - x);
                // rotationAxis=Vector3.back;
                // }

                tile.transform.localPosition = startPos;
                tile.transform.localScale = Vector3.one;

                tile.transform.DOLocalMove(targetPos, duration).SetEase(Ease.OutCubic)
                    .SetDelay(tileDelay);
                tile.transform.DORotate(rotationAxis * 360f * 3, duration, RotateMode.LocalAxisAdd)
                    .SetEase(Ease.OutCubic)
                    .SetDelay(tileDelay);
            }
        }

        await UniTask.Delay(System.TimeSpan.FromSeconds(duration + delay * (innerGridHeight - 1)));
    }

    /// <summary>
    /// 对角线进入
    /// </summary>
    public async UniTask DiagonalIn()
    {
        const float duration = 0.5f;
        const float delay = 0.05f;

        int maxLayer = innerGridWidth + innerGridHeight - 1;

        for (int layer = 1; layer <= maxLayer; layer++)
        {
            int tileCount = Mathf.Min(layer, innerGridWidth, innerGridHeight,
                                   maxLayer - layer + 1);

            for (int i = 0; i < tileCount; i++)
            {
                int x = Mathf.Min(layer, innerGridWidth) - i;
                int y = Mathf.Max(1, layer - innerGridWidth + 1) + i;
                var tile = board.grid[x, y].tile;
                if (tile == null)
                    continue;

                tile.transform.localScale = Vector3.one;
                var targetPos = tile.transform.localPosition;
                tile.transform.localPosition = new Vector3(targetPos.x, targetPos.y, targetPos.z - 10);
                tile.transform.DOLocalMove(targetPos, duration).SetDelay(delay * (layer - 1)).SetEase(Ease.OutCubic);
            }
        }

        await UniTask.Delay(System.TimeSpan.FromSeconds(duration + delay * (maxLayer - 2)));
    }

    /// <summary>
    /// 从中心向四周展开
    /// </summary>
    public async UniTask ExpandFromCenter()
    {
        const float duration = 0.7f;
        const float delay = 0.02f;

        int centerX = (innerGridWidth + 1) / 2;
        int centerY = (innerGridHeight + 1) / 2;

        var sequence = DOTween.Sequence();

        for (int ring = 0; ring <= Mathf.Max(innerGridWidth, innerGridHeight); ring++)
        {
            for (int x = 1; x <= innerGridWidth; x++)
            {
                for (int y = 1; y <= innerGridHeight; y++)
                {
                    int distanceFromCenter = Mathf.Max(Mathf.Abs(x - centerX), Mathf.Abs(y - centerY));
                    if (distanceFromCenter != ring) continue;

                    var tile = board.grid[x, y].tile;
                    if (tile == null) continue;

                    var targetPos = tile.transform.localPosition;
                    tile.transform.localPosition = targetPos;
                    tile.transform.localScale = Vector3.zero;

                    sequence.Insert(delay * ring, tile.transform.DOScale(Vector3.one, duration)
                        .SetEase(Ease.OutBack));
                    sequence.Insert(delay * ring, tile.transform.DORotate(new Vector3(0, 360, 0), duration, RotateMode.LocalAxisAdd)
                        .SetEase(Ease.OutQuad));
                }
            }
        }

        await sequence.AsyncWaitForCompletion().AsUniTask();
    }

    /// <summary>
    /// 波浪形掉落
    /// </summary>
    public async UniTask WaveDrop()
    {
        const float duration = 0.4f;
        const float delay = 0.05f;

        for (int x = 1; x <= innerGridWidth; x++)
        {
            for (int y = 1; y <= innerGridHeight; y++)
            {
                var tile = board.grid[x, y].tile;
                if (tile == null) continue;

                var targetPos = tile.transform.localPosition;
                var startPos = targetPos + Vector3.up * 10f;
                tile.transform.localPosition = startPos;
                tile.transform.localScale = Vector3.one;

                float dropDelay = delay * (x + y);

                float waveOffset = Mathf.Sin(x * 0.5f) * 0.5f;
                Vector3 bouncePos = targetPos + Vector3.up * waveOffset;

                var sequence = DOTween.Sequence();
                sequence.SetDelay(dropDelay)
                    .Append(tile.transform.DOLocalMove(bouncePos, duration * 0.6f).SetEase(Ease.OutQuad))
                    .Append(tile.transform.DOLocalMove(targetPos, duration * 0.4f).SetEase(Ease.InOutQuad));
            }
        }

        await UniTask.Delay(System.TimeSpan.FromSeconds(duration + delay * (innerGridWidth + innerGridHeight)));
    }
}
